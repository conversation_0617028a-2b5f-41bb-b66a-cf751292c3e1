<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CUFE Email Processor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .advanced {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .advanced h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CUFE Email Processor</h1>
        
        <form id="emailForm">
            <div class="form-group">
                <label for="email_host">Email Server (IMAP Host):</label>
                <input type="text" id="email_host" name="email_host" value="imap.gmail.com" required>
            </div>
            
            <div class="form-group">
                <label for="email_port">Port:</label>
                <input type="number" id="email_port" name="email_port" value="993" required>
            </div>
            
            <div class="form-group">
                <label for="email_username">Email Address:</label>
                <input type="email" id="email_username" name="email_username" required>
            </div>
            
            <div class="form-group">
                <label for="email_password">Password (App Password for Gmail):</label>
                <input type="password" id="email_password" name="email_password" required>
            </div>
            
            <div class="form-group">
                <label for="use_ssl">Use SSL:</label>
                <select id="use_ssl" name="use_ssl">
                    <option value="true">Yes (Recommended)</option>
                    <option value="false">No</option>
                </select>
            </div>
            
            <div class="advanced">
                <h3>Advanced Options</h3>
                
                <div class="form-group">
                    <label for="folder">Folder:</label>
                    <input type="text" id="folder" name="folder" value="INBOX">
                </div>
                
                <div class="form-group">
                    <label for="max_emails">Max Emails to Process:</label>
                    <input type="number" id="max_emails" name="max_emails" value="10" min="1" max="100">
                </div>
                
                <div class="form-group">
                    <label for="date_filter">Date Filter (optional):</label>
                    <input type="text" id="date_filter" name="date_filter" placeholder="e.g., SINCE 01-Jan-2024">
                </div>
            </div>
            
            <button type="submit" id="processBtn">🚀 Process Emails</button>
            <button type="button" id="testConnectionBtn">🔗 Test Connection</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        
        document.getElementById('emailForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await processEmails();
        });
        
        document.getElementById('testConnectionBtn').addEventListener('click', async function() {
            await testConnection();
        });
        
        async function processEmails() {
            const formData = new FormData(document.getElementById('emailForm'));
            const data = Object.fromEntries(formData.entries());
            
            // Convert string values to appropriate types
            data.email_port = parseInt(data.email_port);
            data.use_ssl = data.use_ssl === 'true';
            data.max_emails = parseInt(data.max_emails);
            
            // Remove empty date_filter
            if (!data.date_filter) {
                delete data.date_filter;
            }
            
            showResult('Processing emails... This may take a few minutes.', 'loading');
            setButtonsDisabled(true);
            
            try {
                const response = await fetch(`${API_BASE_URL}/process-emails-sync`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`✅ Success!\n\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(`❌ Error: ${result.detail || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
            } finally {
                setButtonsDisabled(false);
            }
        }
        
        async function testConnection() {
            showResult('Testing API connection...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`✅ API Connection Successful!\n\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(`❌ API Error: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Connection Failed: ${error.message}\n\nMake sure the API server is running on ${API_BASE_URL}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        function setButtonsDisabled(disabled) {
            document.getElementById('processBtn').disabled = disabled;
            document.getElementById('testConnectionBtn').disabled = disabled;
        }
        
        // Test connection on page load
        window.addEventListener('load', function() {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
