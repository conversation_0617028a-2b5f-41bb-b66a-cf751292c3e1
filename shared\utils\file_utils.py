"""
File handling utilities
"""

import os
import zipfile
import shutil
from typing import List, Tu<PERSON>, Optional
from pathlib import Path

def ensure_directory(directory_path: str) -> None:
    """
    Ensure a directory exists, create if it doesn't
    
    Args:
        directory_path: Path to the directory
    """
    Path(directory_path).mkdir(parents=True, exist_ok=True)

def get_file_size(file_path: str) -> int:
    """
    Get file size in bytes
    
    Args:
        file_path: Path to the file
    
    Returns:
        File size in bytes
    """
    return os.path.getsize(file_path)

def get_file_extension(filename: str) -> str:
    """
    Get file extension from filename
    
    Args:
        filename: Name of the file
    
    Returns:
        File extension (lowercase, without dot)
    """
    return Path(filename).suffix.lower().lstrip('.')

def is_zip_file(file_path: str) -> bool:
    """
    Check if a file is a valid ZIP file
    
    Args:
        file_path: Path to the file
    
    Returns:
        True if file is a valid ZIP file
    """
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            return zip_file.testzip() is None
    except (zipfile.BadZipFile, FileNotFoundError):
        return False

def extract_zip_file(zip_path: str, extract_to: str) -> List[str]:
    """
    Extract ZIP file contents
    
    Args:
        zip_path: Path to the ZIP file
        extract_to: Directory to extract files to
    
    Returns:
        List of extracted file paths
    
    Raises:
        zipfile.BadZipFile: If ZIP file is corrupted
        FileNotFoundError: If ZIP file doesn't exist
    """
    ensure_directory(extract_to)
    extracted_files = []
    
    with zipfile.ZipFile(zip_path, 'r') as zip_file:
        for file_info in zip_file.filelist:
            if not file_info.is_dir():
                extracted_path = zip_file.extract(file_info, extract_to)
                extracted_files.append(extracted_path)
    
    return extracted_files

def categorize_files(file_paths: List[str]) -> Tuple[List[str], List[str], List[str]]:
    """
    Categorize files by type (XML, PDF, other)
    
    Args:
        file_paths: List of file paths
    
    Returns:
        Tuple of (xml_files, pdf_files, other_files)
    """
    xml_files = []
    pdf_files = []
    other_files = []
    
    for file_path in file_paths:
        extension = get_file_extension(file_path)
        
        if extension == 'xml':
            xml_files.append(file_path)
        elif extension == 'pdf':
            pdf_files.append(file_path)
        else:
            other_files.append(file_path)
    
    return xml_files, pdf_files, other_files

def safe_filename(filename: str) -> str:
    """
    Create a safe filename by removing/replacing problematic characters
    
    Args:
        filename: Original filename
    
    Returns:
        Safe filename
    """
    # Replace problematic characters
    safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_"
    safe_name = "".join(c if c in safe_chars else "_" for c in filename)
    
    # Ensure it's not empty and doesn't start with a dot
    if not safe_name or safe_name.startswith('.'):
        safe_name = "file_" + safe_name
    
    return safe_name

def move_file(source: str, destination: str) -> None:
    """
    Move a file from source to destination
    
    Args:
        source: Source file path
        destination: Destination file path
    """
    ensure_directory(os.path.dirname(destination))
    shutil.move(source, destination)

def copy_file(source: str, destination: str) -> None:
    """
    Copy a file from source to destination
    
    Args:
        source: Source file path
        destination: Destination file path
    """
    ensure_directory(os.path.dirname(destination))
    shutil.copy2(source, destination)

def delete_file(file_path: str) -> None:
    """
    Safely delete a file
    
    Args:
        file_path: Path to the file to delete
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except OSError:
        pass  # Ignore errors when deleting files
